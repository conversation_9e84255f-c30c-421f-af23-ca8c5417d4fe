import React, { useState, useEffect } from 'react';
import { getAllStudents, getStudent, deleteStudent, exportStudentRecord } from '../components/api';
import StudentViewModal from '../components/StudentViewModal';
import ConfirmDialog from '../components/ConfirmDialog';
import NotificationToast from '../components/NotificationToast';

const Search = () => {
    const [students, setStudents] = useState([]);
    const [selectedStudent, setSelectedStudent] = useState(null);
    const [showViewModal, setShowViewModal] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [studentToDelete, setStudentToDelete] = useState(null);
    const [notification, setNotification] = useState(null);
    const [loading, setLoading] = useState(true);
    const [viewLoading, setViewLoading] = useState(false);
    const [saveLoading, setSaveLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [genderFilter, setGenderFilter] = useState('');
    const [bloodGroupFilter, setBloodGroupFilter] = useState('');

    useEffect(() => {
        fetchStudents();
    }, []);

    const fetchStudents = async () => {
        try {
            const data = await getAllStudents();
            setStudents(data);
        } catch (error) {
            console.error('Error fetching students:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleStudentSelect = async (studentId) => {
        try {
            const studentData = await getStudent(studentId);
            setSelectedStudent(studentData);
        } catch (error) {
            console.error('Error fetching student details:', error);
        }
    };

    const handleViewStudent = async (studentId) => {
        try {
            setViewLoading(true);
            const studentData = await getStudent(studentId);
            setSelectedStudent(studentData);
            setShowViewModal(true);
        } catch (error) {
            console.error('Error fetching student details:', error);
            showNotification('Error fetching student details', 'error');
        } finally {
            setViewLoading(false);
        }
    };





    const handleSaveStudent = async (studentId) => {
        try {
            setSaveLoading(true);
            await exportStudentRecord(studentId, 'pdf');
            showNotification('Student record saved as PDF successfully!', 'success');
        } catch (error) {
            console.error('Error saving student record:', error);
            showNotification('Error saving student record', 'error');
        } finally {
            setSaveLoading(false);
        }
    };

    const handleDeleteStudent = (studentId, studentName) => {
        setStudentToDelete({ id: studentId, name: studentName });
        setShowDeleteConfirm(true);
    };

    const confirmDelete = async () => {
        if (studentToDelete) {
            try {
                setLoading(true);
                await deleteStudent(studentToDelete.id);
                showNotification(`Student ${studentToDelete.name} deleted successfully!`, 'success');

                // Refresh the students list
                await fetchStudents();

                setShowDeleteConfirm(false);
                setStudentToDelete(null);
            } catch (error) {
                console.error('Error deleting student:', error);
                showNotification('Error deleting student', 'error');
            } finally {
                setLoading(false);
            }
        }
    };

    const showNotification = (message, type = 'success') => {
        setNotification({ message, type });
    };

    const filteredStudents = students.filter(student => {
        const matchesSearch = student.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            student.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (student.email && student.email.toLowerCase().includes(searchTerm.toLowerCase()));

        const matchesGender = !genderFilter || student.gender === genderFilter;
        const matchesBloodGroup = !bloodGroupFilter || student.blood_group === bloodGroupFilter;

        return matchesSearch && matchesGender && matchesBloodGroup;
    });

    if (loading) return (
        <div className="search-container">
            <div className="loading-state">
                <div className="loading-spinner">
                    <div className="spinner"></div>
                    <p>Loading students...</p>
                </div>
            </div>
        </div>
    );

    return (
        <div className="search-container">
            <h1>Search Students</h1>

            <div className="search-controls-wrapper">
                <div className="search-input">
                    <input
                        type="text"
                        placeholder="Search by name or email..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>

                <div className="search-filters">
                    <select
                        value={genderFilter}
                        onChange={(e) => setGenderFilter(e.target.value)}
                        className="filter-select"
                    >
                        <option value="">All Genders</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                    </select>

                    <select
                        value={bloodGroupFilter}
                        onChange={(e) => setBloodGroupFilter(e.target.value)}
                        className="filter-select"
                    >
                        <option value="">All Blood Groups</option>
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                    </select>

                    {(genderFilter || bloodGroupFilter || searchTerm) && (
                        <button
                            className="clear-filters-btn"
                            onClick={() => {
                                setSearchTerm('');
                                setGenderFilter('');
                                setBloodGroupFilter('');
                            }}
                        >
                            Clear Filters
                        </button>
                    )}
                </div>
            </div>

            <div className="search-results">
                <div className="students-list">
                    <div className="results-header">
                        <h3>Students ({filteredStudents.length})</h3>
                        {(searchTerm || genderFilter || bloodGroupFilter) && (
                            <div className="search-summary">
                                <span className="search-info">
                                    {searchTerm && `Search: "${searchTerm}"`}
                                    {genderFilter && ` • Gender: ${genderFilter}`}
                                    {bloodGroupFilter && ` • Blood: ${bloodGroupFilter}`}
                                </span>
                            </div>
                        )}
                    </div>
                    {filteredStudents.length === 0 ? (
                        <div className="empty-state">
                            <div className="empty-icon">🔍</div>
                            <h4>No students found</h4>
                            <p>Try adjusting your search criteria or filters</p>
                        </div>
                    ) : (
                        filteredStudents.map((student, index) => (
                        <div
                            key={student.student_id}
                            className="student-item"
                            style={{ animationDelay: `${index * 0.1}s` }}
                        >
                            <div className="student-info" onClick={() => handleStudentSelect(student.student_id)}>
                                <h4>{student.first_name} {student.last_name}</h4>
                                <div className="student-details">
                                    <p><strong>Email:</strong> {student.email || 'N/A'}</p>
                                    <p><strong>Phone:</strong> {student.phone || 'N/A'}</p>
                                    <p><strong>Gender:</strong> {student.gender || 'N/A'}</p>
                                    <p><strong>Registered:</strong> {new Date(student.registration_date).toLocaleDateString()}</p>
                                </div>
                            </div>
                            <div className="student-actions">
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleViewStudent(student.student_id);
                                    }}
                                    className="action-btn view-btn"
                                    title="View Student Record"
                                    disabled={viewLoading}
                                >
                                    {viewLoading ? '⏳ Loading...' : '👁️ View'}
                                </button>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleSaveStudent(student.student_id);
                                    }}
                                    className="action-btn save-btn"
                                    title="Save Student Record as PDF"
                                    disabled={saveLoading}
                                >
                                    {saveLoading ? '📄 Generating PDF...' : '📄 Save PDF'}
                                </button>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleDeleteStudent(student.student_id, `${student.first_name} ${student.last_name}`);
                                    }}
                                    className="action-btn delete-btn"
                                    title="Delete Student Record"
                                    disabled={loading}
                                >
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                        ))
                    )}
                </div>

                {selectedStudent && (
                    <div className="student-details">
                        <h3>Student Details</h3>
                        <div className="detail-section">
                            <h4>Basic Information</h4>
                            <p><strong>Name:</strong> {selectedStudent.first_name} {selectedStudent.last_name}</p>
                            <p><strong>Date of Birth:</strong> {selectedStudent.dob}</p>
                            <p><strong>Gender:</strong> {selectedStudent.gender}</p>
                            <p><strong>Email:</strong> {selectedStudent.email || 'N/A'}</p>
                            <p><strong>Phone:</strong> {selectedStudent.phone || 'N/A'}</p>
                            <p><strong>Blood Group:</strong> {selectedStudent.blood_group || 'N/A'}</p>
                        </div>

                        {selectedStudent.parents.length > 0 && (
                            <div className="detail-section">
                                <h4>Parents/Guardians</h4>
                                {selectedStudent.parents.map(parent => (
                                    <div key={parent.parent_id} className="parent-info">
                                        <p><strong>{parent.relation}:</strong> {parent.full_name}</p>
                                        <p>Occupation: {parent.occupation || 'N/A'}</p>
                                        <p>Phone: {parent.phone}</p>
                                        <p>Email: {parent.email || 'N/A'}</p>
                                    </div>
                                ))}
                            </div>
                        )}

                        {selectedStudent.addresses.length > 0 && (
                            <div className="detail-section">
                                <h4>Addresses</h4>
                                {selectedStudent.addresses.map(address => (
                                    <div key={address.address_id} className="address-info">
                                        <p><strong>{address.address_type}:</strong></p>
                                        <p>{address.street}</p>
                                        <p>{address.city}, {address.state} {address.postal_code}</p>
                                        <p>{address.country}</p>
                                    </div>
                                ))}
                            </div>
                        )}

                        {selectedStudent.academic_records.length > 0 && (
                            <div className="detail-section">
                                <h4>Academic Records</h4>
                                {selectedStudent.academic_records.map(record => (
                                    <div key={record.record_id} className="academic-info">
                                        <p><strong>{record.institution_name}</strong> ({record.year})</p>
                                        <p>Grade: {record.grade_level}</p>
                                        <p>GPA: {record.gpa || 'N/A'}</p>
                                        <p>Remarks: {record.remarks || 'N/A'}</p>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                )}
            </div>

            {/* Student View Modal */}
            {showViewModal && selectedStudent && (
                <StudentViewModal
                    student={selectedStudent}
                    onClose={() => {
                        setShowViewModal(false);
                        setSelectedStudent(null);
                    }}
                />
            )}




            {/* Delete Confirmation Dialog */}
            {showDeleteConfirm && studentToDelete && (
                <ConfirmDialog
                    title="Delete Student Record"
                    message={`Are you sure you want to delete the record for ${studentToDelete.name}? This action cannot be undone.`}
                    confirmText="Delete"
                    cancelText="Cancel"
                    onConfirm={confirmDelete}
                    onCancel={() => {
                        setShowDeleteConfirm(false);
                        setStudentToDelete(null);
                    }}
                />
            )}

            {/* Notification Toast */}
            {notification && (
                <NotificationToast
                    message={notification.message}
                    type={notification.type}
                    onClose={() => setNotification(null)}
                />
            )}
        </div>
    );
};

export default Search;
