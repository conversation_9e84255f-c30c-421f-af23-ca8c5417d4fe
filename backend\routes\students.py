from flask import Blueprint, request, jsonify, send_file
from datetime import datetime
from models import db, Student, Parent, Address, AcademicRecord
import json
import csv
import io
import sqlite3
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT

students_bp = Blueprint('students', __name__)

@students_bp.route('/', methods=['POST'])
def create_student():
    data = request.get_json()
    
    try:
        dob = datetime.strptime(data['dob'], '%Y-%m-%d').date()
        
        student = Student(
            first_name=data['first_name'],
            last_name=data['last_name'],
            dob=dob,
            gender=data['gender'],
            email=data.get('email'),
            phone=data.get('phone'),
            blood_group=data.get('blood_group'),
            height=data.get('height'),
            weight=data.get('weight'),
            medical_conditions=data.get('medical_conditions')
            # nationality=data.get('nationality')  # Temporarily disabled due to schema issue
        )
        
        db.session.add(student)
        db.session.commit()
        
        return jsonify({
            'message': 'Student created successfully',
            'student_id': student.student_id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@students_bp.route('/<int:student_id>', methods=['GET'])
def get_student(student_id):
    student = Student.query.get_or_404(student_id)
    
    student_data = {
        'student_id': student.student_id,
        'first_name': student.first_name,
        'last_name': student.last_name,
        'dob': student.dob.isoformat(),
        'gender': student.gender,
        'email': student.email,
        'phone': student.phone,
        'blood_group': student.blood_group,
        'height': student.height,
        'weight': student.weight,
        'medical_conditions': student.medical_conditions,
        # 'nationality': student.nationality,  # Temporarily disabled due to schema issue
        'registration_date': student.registration_date.isoformat(),
        'parents': [{
            'parent_id': p.parent_id,
            'relation': p.relation,
            'full_name': p.full_name,
            'occupation': p.occupation,
            # 'workplace': p.workplace,  # Temporarily disabled due to schema issue
            'email': p.email,
            'phone': p.phone
        } for p in student.parents],
        'addresses': [{
            'address_id': a.address_id,
            'address_type': a.address_type,
            'street': a.street,
            'city': a.city,
            'state': a.state,
            'postal_code': a.postal_code,
            'country': a.country
        } for a in student.addresses],
        'academic_records': [{
            'record_id': r.record_id,
            'institution_name': r.institution_name,
            'year': r.year,
            'grade_level': r.grade_level,
            'gpa': r.gpa,
            'remarks': r.remarks
        } for r in student.academic_records]
    }
    
    return jsonify(student_data)

@students_bp.route('/<int:student_id>', methods=['DELETE'])
def delete_student(student_id):
    student = Student.query.get_or_404(student_id)
    
    try:
        db.session.delete(student)
        db.session.commit()
        return jsonify({'message': 'Student deleted successfully'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@students_bp.route('/', methods=['GET'])
def list_students():
    students = Student.query.all()
    students_list = [{
        'student_id': s.student_id,
        'first_name': s.first_name,
        'last_name': s.last_name,
        'dob': s.dob.isoformat() if s.dob else None,
        'gender': s.gender,
        'email': s.email,
        'phone': s.phone,
        'blood_group': s.blood_group,
        'height': s.height,
        'weight': s.weight,
        'medical_conditions': s.medical_conditions,
        'registration_date': s.registration_date.isoformat()
    } for s in students]

    return jsonify(students_list)

@students_bp.route('/<int:student_id>/export/<format>', methods=['GET'])
def export_student_record(student_id, format):
    student = Student.query.get_or_404(student_id)

    student_data = {
        'student_id': student.student_id,
        'first_name': student.first_name,
        'last_name': student.last_name,
        'dob': student.dob.isoformat(),
        'gender': student.gender,
        'email': student.email,
        'phone': student.phone,
        'blood_group': student.blood_group,
        'height': student.height,
        'weight': student.weight,
        'medical_conditions': student.medical_conditions,
        'registration_date': student.registration_date.isoformat(),
        'parents': [{
            'parent_id': p.parent_id,
            'relation': p.relation,
            'full_name': p.full_name,
            'occupation': p.occupation,
            'email': p.email,
            'phone': p.phone
        } for p in student.parents],
        'addresses': [{
            'address_id': a.address_id,
            'address_type': a.address_type,
            'street': a.street,
            'city': a.city,
            'state': a.state,
            'postal_code': a.postal_code,
            'country': a.country
        } for a in student.addresses],
        'academic_records': [{
            'record_id': r.record_id,
            'institution_name': r.institution_name,
            'year': r.year,
            'grade_level': r.grade_level,
            'gpa': r.gpa,
            'remarks': r.remarks
        } for r in student.academic_records]
    }

    if format.lower() == 'json':
        output = io.StringIO()
        json.dump(student_data, output, indent=2)
        output.seek(0)

        return send_file(
            io.BytesIO(output.getvalue().encode()),
            mimetype='application/json',
            as_attachment=True,
            download_name=f'student_{student_id}_{student.first_name}_{student.last_name}.json'
        )

    elif format.lower() == 'csv':
        output = io.StringIO()
        writer = csv.writer(output)

        # Write student basic info
        writer.writerow(['Field', 'Value'])
        writer.writerow(['Student ID', student_data['student_id']])
        writer.writerow(['Name', f"{student_data['first_name']} {student_data['last_name']}"])
        writer.writerow(['Date of Birth', student_data['dob']])
        writer.writerow(['Gender', student_data['gender']])
        writer.writerow(['Email', student_data['email'] or 'N/A'])
        writer.writerow(['Phone', student_data['phone'] or 'N/A'])
        writer.writerow(['Blood Group', student_data['blood_group'] or 'N/A'])
        writer.writerow(['Registration Date', student_data['registration_date']])

        output.seek(0)

        return send_file(
            io.BytesIO(output.getvalue().encode()),
            mimetype='text/csv',
            as_attachment=True,
            download_name=f'student_{student_id}_{student.first_name}_{student.last_name}.csv'
        )

    elif format.lower() == 'pdf':
        # Create PDF
        output = io.BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

        # Get styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2c3e50')
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20,
            textColor=colors.HexColor('#34495e')
        )

        normal_style = styles['Normal']

        # Build PDF content
        story = []

        # Title
        title = Paragraph("Student Record", title_style)
        story.append(title)
        story.append(Spacer(1, 20))

        # Basic Information
        story.append(Paragraph("Basic Information", heading_style))
        basic_data = [
            ['Field', 'Value'],
            ['Student ID', str(student_data['student_id'])],
            ['Name', f"{student_data['first_name']} {student_data['last_name']}"],
            ['Date of Birth', student_data['dob']],
            ['Gender', student_data['gender']],
            ['Email', student_data['email'] or 'N/A'],
            ['Phone', student_data['phone'] or 'N/A'],
            ['Blood Group', student_data['blood_group'] or 'N/A'],
            ['Height', f"{student_data['height']} cm" if student_data['height'] else 'N/A'],
            ['Weight', f"{student_data['weight']} kg" if student_data['weight'] else 'N/A'],
            ['Medical Conditions', student_data['medical_conditions'] or 'None'],
            ['Registration Date', student_data['registration_date']]
        ]

        basic_table = Table(basic_data, colWidths=[2*inch, 4*inch])
        basic_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 1), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')])
        ]))

        story.append(basic_table)
        story.append(Spacer(1, 20))

        # Parents/Guardians
        if student_data['parents']:
            story.append(Paragraph("Parents/Guardians", heading_style))
            parent_data = [['Relation', 'Name', 'Phone', 'Email', 'Occupation']]
            for parent in student_data['parents']:
                parent_data.append([
                    parent['relation'],
                    parent['full_name'],
                    parent['phone'],
                    parent['email'] or 'N/A',
                    parent['occupation'] or 'N/A'
                ])

            parent_table = Table(parent_data, colWidths=[1*inch, 1.5*inch, 1.2*inch, 1.5*inch, 1.3*inch])
            parent_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#27ae60')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')])
            ]))

            story.append(parent_table)
            story.append(Spacer(1, 20))

        # Addresses
        if student_data['addresses']:
            story.append(Paragraph("Addresses", heading_style))
            for addr in student_data['addresses']:
                addr_text = f"<b>{addr['address_type']}:</b><br/>"
                addr_text += f"{addr['street']}<br/>"
                addr_text += f"{addr['city']}, {addr['state']} {addr['postal_code']}<br/>"
                addr_text += f"{addr['country']}"
                story.append(Paragraph(addr_text, normal_style))
                story.append(Spacer(1, 10))

        # Academic Records
        if student_data['academic_records']:
            story.append(Paragraph("Academic Records", heading_style))
            academic_data = [['Institution', 'Year', 'Grade Level', 'GPA', 'Remarks']]
            for record in student_data['academic_records']:
                academic_data.append([
                    record['institution_name'],
                    str(record['year']),
                    record['grade_level'],
                    str(record['gpa']) if record['gpa'] else 'N/A',
                    record['remarks'] or 'N/A'
                ])

            academic_table = Table(academic_data, colWidths=[2*inch, 0.8*inch, 1*inch, 0.8*inch, 1.9*inch])
            academic_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#e74c3c')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')])
            ]))

            story.append(academic_table)

        # Build PDF
        doc.build(story)
        output.seek(0)

        return send_file(
            output,
            mimetype='application/pdf',
            as_attachment=True,
            download_name=f'student_{student_id}_{student.first_name}_{student.last_name}.pdf'
        )

    else:
        return jsonify({'error': 'Unsupported format'}), 400