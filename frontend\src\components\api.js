const API_BASE_URL = 'http://127.0.0.1:5000/api';

export const createStudent = async (studentData) => {
    const response = await fetch(`${API_BASE_URL}/students/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(studentData),
    });
    if (!response.ok) {
        throw new Error('Failed to create student');
    }
    return await response.json();
};

export const addParent = async (parentData) => {
    const response = await fetch(`${API_BASE_URL}/parents/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(parentData),
    });
    if (!response.ok) {
        throw new Error('Failed to add parent');
    }
    return await response.json();
};

export const addAddress = async (addressData) => {
    const response = await fetch(`${API_BASE_URL}/address/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(addressData),
    });
    if (!response.ok) {
        throw new Error('Failed to add address');
    }
    return await response.json();
};

export const addAcademicRecord = async (recordData) => {
    const response = await fetch(`${API_BASE_URL}/academic/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(recordData),
    });
    if (!response.ok) {
        throw new Error('Failed to add academic record');
    }
    return await response.json();
};

export const getStudent = async (studentId) => {
    const response = await fetch(`${API_BASE_URL}/students/${studentId}`);
    if (!response.ok) {
        throw new Error('Failed to fetch student');
    }
    return await response.json();
};

export const getAllStudents = async () => {
    const response = await fetch(`${API_BASE_URL}/students/`);
    if (!response.ok) {
        throw new Error('Failed to fetch students');
    }
    return await response.json();
};

export const deleteStudent = async (studentId) => {
    const response = await fetch(`${API_BASE_URL}/students/${studentId}`, {
        method: 'DELETE',
    });
    if (!response.ok) {
        throw new Error('Failed to delete student');
    }
    return await response.json();
};

export const exportDatabase = async (format) => {
    const response = await fetch(`${API_BASE_URL}/export/${format}`, {
        method: 'GET',
    });
    if (!response.ok) {
        throw new Error(`Failed to export database as ${format}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `student_database.${format}`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
};

export const exportStudentRecord = async (studentId, format = 'pdf') => {
    const response = await fetch(`${API_BASE_URL}/students/${studentId}/export/${format}`, {
        method: 'GET',
    });
    if (!response.ok) {
        throw new Error(`Failed to export student record as ${format}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `student_${studentId}.${format}`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
};