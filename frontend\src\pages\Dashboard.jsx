import React, { useState, useEffect } from 'react';
import { getAllStudents, getStudent, deleteStudent, exportStudentRecord } from '../components/api';
import StudentApplicationForm from '../components/StudentApplicationForm';
import StudentViewModal from '../components/StudentViewModal';
import ConfirmDialog from '../components/ConfirmDialog';
import NotificationToast from '../components/NotificationToast';

const Dashboard = () => {
    const [students, setStudents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [viewLoading, setViewLoading] = useState(false);
    const [saveLoading, setSaveLoading] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState(null);
    const [showApplicationForm, setShowApplicationForm] = useState(false);
    const [showViewModal, setShowViewModal] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [studentToDelete, setStudentToDelete] = useState(null);
    const [notification, setNotification] = useState(null);
    const [stats, setStats] = useState({
        totalStudents: 0,
        maleStudents: 0,
        femaleStudents: 0,
        recentRegistrations: 0,
        averageAge: 0,
        bloodGroupDistribution: {}
    });

    useEffect(() => {
        fetchDashboardData();
    }, []);

    const fetchDashboardData = async () => {
        try {
            setLoading(true);
            const data = await getAllStudents();
            setStudents(data);
            calculateStats(data);
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
        } finally {
            setLoading(false);
        }
    };

    const calculateStats = (studentData) => {
        const total = studentData.length;
        const male = studentData.filter(s => s.gender === 'Male').length;
        const female = studentData.filter(s => s.gender === 'Female').length;

        // Recent registrations (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recent = studentData.filter(s =>
            new Date(s.registration_date) > thirtyDaysAgo
        ).length;

        // Calculate average age
        const currentYear = new Date().getFullYear();
        const ages = studentData.map(s => {
            const birthYear = new Date(s.dob || '2000-01-01').getFullYear();
            return currentYear - birthYear;
        });
        const averageAge = ages.length > 0 ? Math.round(ages.reduce((a, b) => a + b, 0) / ages.length) : 0;

        // Blood group distribution
        const bloodGroups = {};
        studentData.forEach(s => {
            if (s.blood_group) {
                bloodGroups[s.blood_group] = (bloodGroups[s.blood_group] || 0) + 1;
            }
        });

        setStats({
            totalStudents: total,
            maleStudents: male,
            femaleStudents: female,
            recentRegistrations: recent,
            averageAge,
            bloodGroupDistribution: bloodGroups
        });
    };

    const handleViewStudent = async (studentId) => {
        try {
            setViewLoading(true);
            const studentData = await getStudent(studentId);
            setSelectedStudent(studentData);
            setShowViewModal(true);
        } catch (error) {
            console.error('Error fetching student details:', error);
            showNotification('Error fetching student details', 'error');
        } finally {
            setViewLoading(false);
        }
    };





    const handleSaveStudent = async (studentId) => {
        try {
            setSaveLoading(true);
            await exportStudentRecord(studentId, 'pdf');
            showNotification('Student record saved as PDF successfully!', 'success');
        } catch (error) {
            console.error('Error saving student record:', error);
            showNotification('Error saving student record', 'error');
        } finally {
            setSaveLoading(false);
        }
    };

    const handleDeleteStudent = (studentId, studentName) => {
        setStudentToDelete({ id: studentId, name: studentName });
        setShowDeleteConfirm(true);
    };

    const confirmDelete = async () => {
        if (studentToDelete) {
            try {
                setLoading(true);
                await deleteStudent(studentToDelete.id);
                showNotification(`Student ${studentToDelete.name} deleted successfully!`, 'success');

                // Refresh the students list
                await fetchDashboardData();

                setShowDeleteConfirm(false);
                setStudentToDelete(null);
            } catch (error) {
                console.error('Error deleting student:', error);
                showNotification('Error deleting student', 'error');
            } finally {
                setLoading(false);
            }
        }
    };

    const showNotification = (message, type = 'success') => {
        setNotification({ message, type });
    };

    if (loading) return (
        <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading dashboard...</p>
        </div>
    );

    return (
        <div className="dashboard-container">
            <div className="dashboard-header">
                <h1>Student Management Dashboard</h1>
            </div>

            <div className="stats-grid">
                <div className="stat-card primary">
                    <div className="stat-icon">👥</div>
                    <div className="stat-content">
                        <h3>Total Students</h3>
                        <div className="stat-number">{stats.totalStudents}</div>
                    </div>
                </div>

                <div className="stat-card male">
                    <div className="stat-icon">👨‍🎓</div>
                    <div className="stat-content">
                        <h3>Male Students</h3>
                        <div className="stat-number">{stats.maleStudents}</div>
                        <div className="stat-percentage">
                            {stats.totalStudents > 0 ? Math.round((stats.maleStudents / stats.totalStudents) * 100) : 0}%
                        </div>
                    </div>
                </div>

                <div className="stat-card female">
                    <div className="stat-icon">👩‍🎓</div>
                    <div className="stat-content">
                        <h3>Female Students</h3>
                        <div className="stat-number">{stats.femaleStudents}</div>
                        <div className="stat-percentage">
                            {stats.totalStudents > 0 ? Math.round((stats.femaleStudents / stats.totalStudents) * 100) : 0}%
                        </div>
                    </div>
                </div>

                <div className="stat-card recent">
                    <div className="stat-icon">📅</div>
                    <div className="stat-content">
                        <h3>Recent Registrations</h3>
                        <div className="stat-number">{stats.recentRegistrations}</div>
                        <div className="stat-subtitle">Last 30 days</div>
                    </div>
                </div>

                <div className="stat-card age">
                    <div className="stat-icon">🎂</div>
                    <div className="stat-content">
                        <h3>Average Age</h3>
                        <div className="stat-number">{stats.averageAge}</div>
                        <div className="stat-subtitle">years</div>
                    </div>
                </div>

                <div className="stat-card blood">
                    <div className="stat-icon">🩸</div>
                    <div className="stat-content">
                        <h3>Blood Groups</h3>
                        <div className="blood-group-chart">
                            {Object.entries(stats.bloodGroupDistribution).map(([group, count]) => (
                                <div key={group} className="blood-group-item">
                                    <span className="blood-group-label">{group}</span>
                                    <span className="blood-group-count">{count}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            <div className="recent-students">
                <h2>Recent Students</h2>
                <div className="students-table">
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Registration Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {students.slice(0, 10).map(student => (
                                <tr key={student.student_id}>
                                    <td>{student.student_id}</td>
                                    <td>{student.first_name} {student.last_name}</td>
                                    <td>{student.email || 'N/A'}</td>
                                    <td>{student.phone || 'N/A'}</td>
                                    <td>{new Date(student.registration_date).toLocaleDateString()}</td>
                                    <td>
                                        <div className="action-buttons">
                                            <button
                                                onClick={() => handleViewStudent(student.student_id)}
                                                className="action-btn view-btn"
                                                title="View Student Record"
                                                disabled={viewLoading}
                                            >
                                                {viewLoading ? '⏳ Loading...' : '👁️ View'}
                                            </button>
                                            <button
                                                onClick={() => handleSaveStudent(student.student_id)}
                                                className="action-btn save-btn"
                                                title="Save Student Record as PDF"
                                                disabled={saveLoading}
                                            >
                                                {saveLoading ? '📄 Generating PDF...' : '📄 Save PDF'}
                                            </button>
                                            <button
                                                onClick={() => handleDeleteStudent(student.student_id, `${student.first_name} ${student.last_name}`)}
                                                className="action-btn delete-btn"
                                                title="Delete Student Record"
                                                disabled={loading}
                                            >
                                                🗑️ Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Student View Modal */}
            {showViewModal && selectedStudent && (
                <StudentViewModal
                    student={selectedStudent}
                    onClose={() => {
                        setShowViewModal(false);
                        setSelectedStudent(null);
                    }}
                />
            )}


            {/* Student Application Form Modal */}
            {showApplicationForm && selectedStudent && (
                <StudentApplicationForm
                    student={selectedStudent}
                    onClose={() => {
                        setShowApplicationForm(false);
                        setSelectedStudent(null);
                    }}
                />
            )}



            {/* Delete Confirmation Dialog */}
            {showDeleteConfirm && studentToDelete && (
                <ConfirmDialog
                    title="Delete Student Record"
                    message={`Are you sure you want to delete the record for ${studentToDelete.name}? This action cannot be undone.`}
                    confirmText="Delete"
                    cancelText="Cancel"
                    onConfirm={confirmDelete}
                    onCancel={() => {
                        setShowDeleteConfirm(false);
                        setStudentToDelete(null);
                    }}
                    type="danger"
                />
            )}

            {/* Notification Toast */}
            {notification && (
                <NotificationToast
                    message={notification.message}
                    type={notification.type}
                    onClose={() => setNotification(null)}
                />
            )}
        </div>
    );
};

export default Dashboard;
